# CLAUDE.md

此文件为Claude Code (claude.ai/code)在本代码库中工作时提供指导。

## 代码库概述

本代码库包含一个修改版的Augment VSCode扩展（版本0.524.1），该版本针对中国大陆网络环境进行了网络性能优化。原版扩展是一个AI驱动的编程平台，提供Agent、代码补全、聊天和Next Edit等功能，帮助软件工程师更高效地工作。

修改版本包含多项网络优化增强功能，以提高在网络条件较差环境下的稳定性和性能。

## 主要架构组件

### 扩展主体结构
- **入口点**: `./out/extension.js`（由TypeScript源码编译）
- **配置**: `package.json`中的`contributes.configuration`下定义的大量配置选项
- **Web视图**: `common-webviews/`目录中的UI组件，用于面板和界面
- **媒体资源**: `media/`目录中的图标、图像和字体

### 核心功能
1. **Agent驱动工程** - 理解代码库上下文的AI代理
2. **智能聊天** - 基于对话的编码辅助，支持智能应用
3. **Next Edit** - 跨代码、测试和文档的逐步修改指导
4. **代码补全** - 具备代码库感知的内联代码建议
5. **指令** - 自然语言代码修改

### 网络优化（修改版本）
修改版本包含多项增强功能，以改善网络性能：
- 代理服务器配置支持
- 连接超时设置（默认30秒）
- 请求超时设置（默认60秒）
- 重试机制与可配置延迟
- 本地缓存以减少网络请求
- 请求防抖和节流
- HTTP Keep-Alive连接复用
- 并发请求限制
- 请求队列系统

## 开发命令

### 构建命令
```bash
# 带源映射的开发构建
pnpm run build

# 带压缩的生产构建
pnpm run build:prod

# 开发监控模式
pnpm run watch
```

### 测试命令
```bash
# 运行所有测试
pnpm run test

# 带详细日志的测试运行
pnpm run test:debug

# 运行代码检查
pnpm run lint

# 修复代码检查问题
pnpm run lint:fix
```

### 打包命令
```bash
# 打包扩展（生产环境）
pnpm run package-extension-pnpm

# 打包发布版本
pnpm run package-for-release
```

### 开发工具
- **包管理器**: pnpm (v9)
- **构建工具**: esbuild
- **TypeScript**: 用于类型检查和编译
- **测试**: Jest用于单元测试，WebdriverIO用于端到端测试
- **代码检查**: 带TypeScript插件的ESLint

## 关键配置区域

### 网络优化设置
在`package.json`的`augment.advanced.networkOptimization`下：
- 连接和请求超时设置
- 重试机制
- Keep-alive设置
- 并发限制

### 代码补全设置
在`package.json`的`augment.advanced.completions`下：
- 超时和重试配置
- 缓存和防抖设置

### Next Edit设置
在`package.json`的`augment.nextEdit`下：
- 后台建议控制
- 延迟设置

## 重要依赖

### 核心AI库
- `@anthropic-ai/sdk` - Anthropic API客户端
- `@anthropic-ai/vertex-sdk` - Anthropic Vertex AI集成

### UI框架
- `monaco-editor` - 代码编辑器组件
- 用于VSCode面板的自定义webview组件

### 数据管理
- `level` - 用于存储扩展数据的数据库
- `diff` - 文本差异计算
- `fuse.js` - 模糊搜索功能

### 工具库
- `lodash` - 通用工具函数
- `uuid` - 唯一标识符生成
- `semver` - 语义化版本处理
- `simple-git` - Git操作

## 修改扩展

### 配置更改
所有配置更改应在`package.json`的`contributes.configuration`部分进行。确保在`default`对象中正确设置默认值，而不仅仅是在属性定义中。

### 更改后构建
1. 运行`pnpm run build`进行开发构建
2. 运行`pnpm run build:prod`进行生产构建
3. 使用`pnpm run package-extension-pnpm`打包

### 测试更改
1. 使用`pnpm run test`运行所有测试
2. 使用`pnpm run test:debug`获取详细输出
3. 通过安装生成的.vsix文件在VSCode中手动测试

## 打包和分发

### 创建VSIX包
```bash
pnpm run package-extension-pnpm
```

### 安装测试
```bash
code --install-extension [文件名].vsix
```

### 版本管理
在打包前更新`package.json`中的版本号，并在`CHANGELOG.md`中记录更改。

## 网络优化详情

修改版本包含几项关键优化，以改善在网络环境较差情况下的性能：

1. **延长超时**: 连接（30秒）和请求（60秒）超时防止过早失败
2. **重试逻辑**: 可配置的重试次数（最多5次）和渐进式延迟
3. **缓存**: 本地缓存减少冗余网络请求
4. **请求管理**: 防抖、节流和队列控制请求频率
5. **连接复用**: HTTP Keep-Alive提高连接效率
6. **并发控制**: 限制并发请求防止过载
7. **代理支持**: 内置代理配置改善连接性

这些优化通过默认配置值自动启用，用户无需手动设置。