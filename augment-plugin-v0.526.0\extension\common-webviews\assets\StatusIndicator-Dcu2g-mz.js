var A=Object.defineProperty;var M=(e,t,s)=>t in e?A(e,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):e[t]=s;var f=(e,t,s)=>M(e,typeof t!="symbol"?t+"":t,s);import{W as o,h as _}from"./IconButtonAugment-DeYSG9-N.js";import{l as I,f as v,a as b,t as h,b as d,y as V,A as n,V as w,W as C,m as S,I as c,Y as r,Z as $,B as L,H as W,_ as Z,$ as B,K as k,O as E,S as F,G as O}from"./SpinnerAugment-Che_fZ8x.js";import{g as j,a as z}from"./remote-agents-client-BVJSNGEI.js";const Y="remoteAgentStore",q="remoteAgentStore";function D(e){const t=e;return Array.isArray(t==null?void 0:t.agentOverviews)&&Array.isArray(t==null?void 0:t.activeWebviews)&&((t==null?void 0:t.pinnedAgents)===void 0||typeof t.pinnedAgents=="object")}class Q{constructor(t,s=void 0,a,i){f(this,"subscribers",new Set);this._msgBroker=t,this._state=s,this.validateState=a,this._storeId=i,s&&this.setStateInternal(s)}subscribe(t){return this.subscribers.add(t),t(this),()=>{this.subscribers.delete(t)}}notifySubscribers(){this.subscribers.forEach(t=>t(this))}get state(){return this._state}get storeId(){return this._storeId}shouldAcceptMessage(t,s){return t.id===this.storeId&&this.validateState(s)}update(t){const s=t(this._state);s!==void 0&&this.setStateInternal(s)}setState(t){this.setStateInternal(t)}async setStateInternal(t){JSON.stringify(this._state)!==JSON.stringify(t)&&(this._state=t,this._msgBroker.postMessage({type:o.updateSharedWebviewState,data:t,id:this.storeId}))}async fetchStateFromExtension(){const t=await this._msgBroker.send({type:o.getSharedWebviewState,id:this.storeId,data:{}});t.type===o.getSharedWebviewStateResponse&&this.shouldAcceptMessage(t,t.data)&&(this._state=t.data,this.notifySubscribers())}handleMessageFromExtension(t){switch(t.data.type){case o.updateSharedWebviewState:case o.getSharedWebviewStateResponse:return!!this.shouldAcceptMessage(t.data,t.data.data)&&(this._state=t.data.data,this.notifySubscribers(),!0);default:return!1}}}var J=v("<svg><!></svg>");function X(e,t){const s=I(t,["children","$$slots","$$events","$$legacy"]);var a=J();b(a,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 384 512",...s}));var i=h(a);_(i,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="m134.6 51.7-10.8 140.9c-1.1 14.6-8.8 27.8-20.9 36-23.9 16.2-41.8 40.8-49.1 70.3l-1.3 5.1H168v-88c0-13.3 10.7-24 24-24s24 10.7 24 24v88h115.5l-1.3-5.1c-7.4-29.5-25.2-54.1-49.1-70.2-12.1-8.2-19.8-21.5-20.9-36l-10.8-141c-.1-1.2-.1-2.5-.1-3.7H134.8c0 1.2 0 2.5-.1 3.7zM168 352H32c-9.9 0-19.2-4.5-25.2-12.3s-8.2-17.9-5.8-27.5l6.2-25c10.3-41.3 35.4-75.7 68.7-98.3L83.1 96l3.7-48H56c-4.4 0-8.6-1.2-12.2-3.3C36.8 40.5 32 32.8 32 24 32 10.7 42.7 0 56 0h272c13.3 0 24 10.7 24 24 0 8.8-4.8 16.5-11.8 20.7-3.6 2.1-7.7 3.3-12.2 3.3h-30.8l3.7 48 7.1 92.9c33.3 22.6 58.4 57.1 68.7 98.3l6.2 25c2.4 9.6.2 19.7-5.8 27.5S361.7 352 351.9 352h-136v136c0 13.3-10.7 24-24 24s-24-10.7-24-24V352z"/>',!0),d(e,a)}var N=v('<svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M5.5 1C5.22386 1 5 1.22386 5 1.5C5 1.77614 5.22386 2 5.5 2H9.5C9.77614 2 10 1.77614 10 1.5C10 1.22386 9.77614 1 9.5 1H5.5ZM3 3.5C3 3.22386 3.22386 3 3.5 3H5H10H11.5C11.7761 3 12 3.22386 12 3.5C12 3.77614 11.7761 4 11.5 4H11V12C11 12.5523 10.5523 13 10 13H5C4.44772 13 4 12.5523 4 12V4L3.5 4C3.22386 4 3 3.77614 3 3.5ZM5 4H10V12H5V4Z" fill="currentColor"></path></svg>');function t1(e){var t=N();d(e,t)}var R=v('<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><rect width="16" height="16" fill="currentColor" fill-opacity="0.01"></rect><path fill-rule="evenodd" clip-rule="evenodd" d="M3.4718 3.46066C3.65925 3.2732 3.96317 3.2732 4.15062 3.46066L7.35062 6.66066C7.44064 6.75068 7.49121 6.87277 7.49121 7.00007C7.49121 7.12737 7.44064 7.24946 7.35062 7.33949L4.15062 10.5395C3.96317 10.7269 3.65925 10.7269 3.4718 10.5395C3.28435 10.352 3.28435 10.0481 3.4718 9.86067L6.33239 7.00007L3.4718 4.13949C3.28435 3.95203 3.28435 3.64812 3.4718 3.46066Z" fill="currentColor"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M7.86854 10.6132C7.57399 10.6132 7.33521 10.8519 7.33521 11.1465C7.33521 11.441 7.57399 11.6798 7.86854 11.6798H12.1352C12.4298 11.6798 12.6685 11.441 12.6685 11.1465C12.6685 10.8519 12.4298 10.6132 12.1352 10.6132H7.86854Z" fill="currentColor"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M2.13331 1.06665C1.5442 1.06665 1.06664 1.54421 1.06664 2.13332V13.8667C1.06664 14.4558 1.5442 14.9333 2.13331 14.9333H13.8667C14.4558 14.9333 14.9333 14.4558 14.9333 13.8667V2.13332C14.9333 1.54421 14.4558 1.06665 13.8667 1.06665H2.13331ZM2.13331 2.13332H13.8667V13.8667H2.13331V2.13332Z" fill="currentColor"></path></svg>');function s1(e){var t=R();d(e,t)}var G=W('<div><div class="status-dot svelte-v4itgx"></div> <div class="status-label svelte-v4itgx"> </div></div>');function e1(e,t){V(t,!1);const s=S(),a=S();let i=n(t,"status",8),u=n(t,"workspaceStatus",8),p=n(t,"isExpanded",8,!1),g=n(t,"hasUpdates",8,!1);w(()=>(c(i()),c(u()),c(g())),()=>{C(s,j(i(),u(),g()))}),w(()=>r(s),()=>{C(a,z(r(s)))}),$(),L();var l=G();b(l,(H,x)=>({class:"status-indicator-container",role:"status","aria-label":`Agent status: ${r(s)??""}`,title:`Status: ${r(s)??""}`,...H,[B]:x}),[()=>Z(r(a)),()=>({expanded:p(),collapsed:!p()})],"svelte-v4itgx");var m=k(h(l),2),y=h(m);E(()=>F(y,r(s))),d(e,l),O()}export{e1 as S,t1 as T,s1 as a,X as b,q as c,Q as d,Y as e,D as v};
