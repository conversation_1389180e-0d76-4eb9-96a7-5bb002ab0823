# Change Log

该版本为Augment 的网络增强修改版，适用于中国大陆网络环境。

## [Enhanced] - 2025-08-06

### Added
- 代理服务器配置模块，新增代理支持保证网络通畅
- 网络优化配置模块，自动应用连接超时30秒、请求超时60秒等优化参数
- 代码完成功能的重试机制，自动启用最大3次重试和1秒重试间隔
- 本地缓存功能，自动启用减少重复网络请求提升响应速度
- 请求防抖机制，自动设置300毫秒防抖延迟避免频繁触发
- HTTP Keep-Alive连接复用功能，自动启用提升网络连接效率
- 并发请求数量限制，自动限制最大3个并发请求防止过载
- 请求队列管理系统，自动启用合理调度网络请求优先级
- 自动完成触发延迟，设置500毫秒延迟减少频繁触发
- 请求节流功能，自动启用避免过于频繁的API调用

### Changed
- 代码完成超时时间从800毫秒自动延长至5000毫秒，提升网络不稳定环境下的成功率
- 最大等待时间从1600毫秒自动增加到10000毫秒，减少因网络延迟导致的请求失败
- IntelliSense快速建议功能默认关闭，自动减少不必要的网络请求
- Next Edit后台建议功能默认关闭，自动降低网络负载
- Next Edit建议延迟设置为2000毫秒，减少频繁触发
- 完成服务器URL自动配置代理，提升网络连接稳定性
- 所有网络优化配置自动生效，无需用户手动配置

### Enhanced
- 网络连接稳定性显著提升，特别针对中国大陆网络环境进行优化
- 代码完成响应时间更加稳定，减少因网络波动导致的功能异常
- 插件整体性能优化，降低CPU和网络资源占用
- 用户体验改善，减少等待时间和请求失败情况
- 通过代理服务器访问，解决网络连接问题

### Technical Details
- 新增proxyConfig配置对象，支持代理服务器配置
- 新增networkOptimization配置对象，包含7个网络相关参数，默认值自动生效
- completions配置扩展，新增7个性能优化选项，包含缓存和防抖机制
- 触发延迟机制实现，自动完成延迟500ms，建议延迟2000ms
- 请求节流算法集成，自动启用智能控制API调用频率
- 所有优化配置采用自动生效模式，用户安装即享受优化效果
- 网络超时参数优化：连接30s，请求60s，重试5次间隔2s
- 代码完成参数优化：超时5s，最大等待10s，启用缓存和防抖
- 代理服务器配置：自动配置代理参数，提升网络连接质量

---

**修改作者**: dabaotongxue
**基于版本**: vscode-augment-0.526.0