import{l as G,y as at,A as r,m,V as D,Z as st,B as lt,H,C as P,D as B,F as U,b as g,W as c,Y as s,a1 as vt,a7 as tt,L as O,K as J,t as Y,G as nt,J as rt,I as R,X as it,x as ht,ak as dt,a as ft,$ as pt,am as mt,M as p,N as $,O as gt,P as $t,S as yt}from"./SpinnerAugment-Che_fZ8x.js";import{I as bt,b as l,a as wt}from"./IconButtonAugment-DeYSG9-N.js";import{T as xt,a as et}from"./CardAugment-CwPOsQsd.js";import{B as zt}from"./ButtonAugment-BU-9CCfo.js";import{B as Ct,b as Lt}from"./BaseTextInput-BmPg9O7g.js";var Tt=H("<!> <!> <!>",1),Ot=H('<div class="c-successful-button svelte-1dvyzw2"><!></div>');function Dt(K,t){var a;const W=G(t,["children","$$slots","$$events","$$legacy"]),I=G(W,["defaultColor","tooltip","stateVariant","onClick","tooltipDuration","icon","stickyColor","persistOnTooltipClose","tooltipNested"]);at(t,!1);const y=m(),b=m(),L=m();let w,x=r(t,"defaultColor",8),V=r(t,"tooltip",24,()=>{}),F=r(t,"stateVariant",24,()=>{}),X=r(t,"onClick",8),A=r(t,"tooltipDuration",8,1500),u=r(t,"icon",8,!1),M=r(t,"stickyColor",8,!0),Z=r(t,"persistOnTooltipClose",8,!1),N=r(t,"tooltipNested",24,()=>{}),i=m("neutral"),z=m(x()),q=m(void 0),_=m((a=V())==null?void 0:a.neutral);async function S(o){var d;try{c(i,await X()(o)??"neutral")}catch(h){console.error(h),c(i,"failure")}c(_,(d=V())==null?void 0:d[s(i)]),clearTimeout(w),w=setTimeout(()=>{var h;(h=s(q))==null||h(),M()||c(i,"neutral")},A())}D(()=>(s(y),s(b),R(I)),()=>{c(y,I.variant),c(b,it(I,["variant"]))}),D(()=>(R(F()),s(i),s(y)),()=>{var o;c(L,((o=F())==null?void 0:o[s(i)])??s(y))}),D(()=>(s(i),R(x())),()=>{s(i)==="success"?c(z,"success"):s(i)==="failure"?c(z,"error"):c(z,x())}),st(),lt();var v=Ot(),T=Y(v);const n=vt(()=>(R(et),rt(()=>[et.Hover])));xt(T,{onOpenChange:function(o){var d;Z()||o||(clearTimeout(w),w=void 0,c(_,(d=V())==null?void 0:d.neutral),M()||c(i,"neutral"))},get content(){return s(_)},get triggerOn(){return s(n)},get nested(){return N()},get requestClose(){return s(q)},set requestClose(o){c(q,o)},children:(o,d)=>{var h=P(),j=B(h),ot=E=>{bt(E,tt(()=>s(b),{get color(){return s(z)},get variant(){return s(L)},$$events:{click:S,keyup(e){l.call(this,t,e)},keydown(e){l.call(this,t,e)},mousedown(e){l.call(this,t,e)},mouseover(e){l.call(this,t,e)},focus(e){l.call(this,t,e)},mouseleave(e){l.call(this,t,e)},blur(e){l.call(this,t,e)},contextmenu(e){l.call(this,t,e)}},children:(e,Q)=>{var f=Tt(),C=B(f);O(C,t,"iconLeft",{},null);var k=J(C,2);O(k,t,"default",{},null);var ut=J(k,2);O(ut,t,"iconRight",{},null),g(e,f)},$$slots:{default:!0}}))},ct=E=>{zt(E,tt(()=>s(b),{get color(){return s(z)},get variant(){return s(L)},$$events:{click:S,keyup(e){l.call(this,t,e)},keydown(e){l.call(this,t,e)},mousedown(e){l.call(this,t,e)},mouseover(e){l.call(this,t,e)},focus(e){l.call(this,t,e)},mouseleave(e){l.call(this,t,e)},blur(e){l.call(this,t,e)},contextmenu(e){l.call(this,t,e)}},children:(e,Q)=>{var f=P(),C=B(f);O(C,t,"default",{},null),g(e,f)},$$slots:{default:!0,iconLeft:(e,Q)=>{var f=P(),C=B(f);O(C,t,"iconLeft",{},null),g(e,f)},iconRight:(e,Q)=>{var f=P(),C=B(f);O(C,t,"iconRight",{},null),g(e,f)}}}))};U(j,E=>{u()?E(ot):E(ct,!1)}),g(o,h)},$$slots:{default:!0},$$legacy:!0}),g(K,v),nt()}var Rt=H('<label class="c-text-area-label svelte-c1sr7w"> </label>'),At=H('<div class="c-text-area-label-container svelte-c1sr7w"><!> <!></div>'),qt=H("<textarea></textarea>"),Et=H('<div class="c-text-area svelte-c1sr7w"><!> <!></div>');function Ft(K,t){const W=ht(t),I=G(t,["children","$$slots","$$events","$$legacy"]),y=G(I,["label","variant","size","color","resize","textInput","type","value","id"]);at(t,!1);const b=m(),L=m(),w=m();let x=r(t,"label",24,()=>{}),V=r(t,"variant",8,"surface"),F=r(t,"size",8,2),X=r(t,"color",24,()=>{}),A=r(t,"resize",8,"none"),u=r(t,"textInput",28,()=>{}),M=r(t,"type",8,"default"),Z=r(t,"value",12,""),N=r(t,"id",24,()=>{});function i(){if(!u())return;u(u().style.height="auto",!0);const v=.8*window.innerHeight,T=Math.min(u().scrollHeight,v);u(u().style.height=`${T}px`,!0),u(u().style.overflowY=u().scrollHeight>v?"auto":"hidden",!0)}dt(()=>{if(u()){requestAnimationFrame(i);const v=()=>i();return window.addEventListener("resize",v),()=>{window.removeEventListener("resize",v)}}}),D(()=>R(N()),()=>{c(b,N()||`text-field-${Math.random().toString(36).substring(2,11)}`)}),D(()=>(s(L),s(w),R(y)),()=>{c(L,y.class),c(w,it(y,["class"]))}),st(),lt();var z=Et(),q=Y(z),_=v=>{var T=At(),n=Y(T),a=d=>{var h=Rt(),j=Y(h);gt(()=>{$t(h,"for",s(b)),yt(j,x())}),g(d,h)};U(n,d=>{x()&&d(a)});var o=J(n,2);O(o,t,"topRightAction",{},null),g(v,T)};U(q,v=>{R(x()),rt(()=>x()||W.topRightAction)&&v(_)});var S=J(q,2);Ct(S,{get type(){return M()},get variant(){return V()},get size(){return F()},get color(){return X()},children:(v,T)=>{var n=qt();ft(n,a=>({id:s(b),spellCheck:"false",class:`c-text-area__input c-base-text-input__input ${s(L)}`,...s(w),[pt]:a}),[()=>({"c-textarea--resize-none":A()==="none","c-textarea--resize-both":A()==="both","c-textarea--resize-horizontal":A()==="horizontal","c-textarea--resize-vertical":A()==="vertical"})],"svelte-c1sr7w"),mt(n,a=>u(a),()=>u()),p(()=>Lt(n,Z)),wt(n,a=>function(o){requestAnimationFrame(i);const d=()=>i();o.addEventListener("input",d);const h=new ResizeObserver(i);return h.observe(o),{destroy(){o.removeEventListener("input",d),h.disconnect()}}}(a)),p(()=>$("click",n,function(a){l.call(this,t,a)})),p(()=>$("focus",n,function(a){l.call(this,t,a)})),p(()=>$("keydown",n,function(a){l.call(this,t,a)})),p(()=>$("change",n,function(a){l.call(this,t,a)})),p(()=>$("input",n,function(a){l.call(this,t,a)})),p(()=>$("keyup",n,function(a){l.call(this,t,a)})),p(()=>$("blur",n,function(a){l.call(this,t,a)})),p(()=>$("select",n,function(a){l.call(this,t,a)})),p(()=>$("mouseup",n,function(a){l.call(this,t,a)})),g(v,n)},$$slots:{default:!0}}),g(K,z),nt()}export{Dt as S,Ft as T};
