{"name": "module-error", "version": "1.0.2", "description": "Create errors with code and cause properties", "license": "MIT", "author": "<PERSON>", "scripts": {"test": "standard && tsc && hallmark && node test", "build": "tsc -d --emitDeclarationOnly --noEmit false"}, "types": "./index.d.ts", "files": ["index.js", "index.d.ts", "CHANGELOG.md"], "devDependencies": {"@types/node": "^17.0.6", "@voxpelli/tsconfig": "^3.0.0", "hallmark": "^4.0.0", "standard": "^16.0.4", "tape": "^5.3.1", "typescript": "^4.4.4"}, "keywords": ["cause", "code", "error"], "repository": "vweevers/module-error", "bugs": "https://github.com/vweevers/module-error/issues", "homepage": "https://github.com/vweevers/module-error", "engines": {"node": ">=10"}}