# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Repository Overview

This repository contains a modified version of the Augment VSCode extension (version 0.524.1) that has been optimized for network performance in mainland China. The original extension is an AI-powered coding platform that provides features like Agent, Completions, Chat, and Next Edit to help software engineers work more efficiently.

The modified version includes several network optimization enhancements to improve stability and performance in environments with challenging network conditions.

## Key Architecture Components

### Main Extension Structure
- **Entry Point**: `./out/extension.js` (compiled from TypeScript sources)
- **Configuration**: Extensive configuration options defined in `package.json` under `contributes.configuration`
- **Webviews**: UI components in `common-webviews/` directory for panels and interfaces
- **Media Assets**: Icons, images, and fonts in the `media/` directory

### Core Features
1. **Agent powered engineering** - AI agent that understands codebase context
2. **Intelligent Chat** - Conversation-based coding assistance with Smart Apply
3. **Next Edit** - Turn-by-turn directions for making changes across code, tests, and docs
4. **Code Completions** - Inline code suggestions with codebase awareness
5. **Instructions** - Natural language code modifications

### Network Optimizations (Modified Version)
The modified version includes several enhancements for improved network performance:
- Proxy server configuration support
- Connection timeout settings (30s default)
- Request timeout settings (60s default)
- Retry mechanisms with configurable delays
- Local caching to reduce network requests
- Request debouncing and throttling
- HTTP Keep-Alive connection reuse
- Concurrent request limiting
- Request queuing system

## Development Commands

### Build Commands
```bash
# Development build with source maps
pnpm run build

# Production build with minification
pnpm run build:prod

# Watch mode for development
pnpm run watch
```

### Testing Commands
```bash
# Run all tests
pnpm run test

# Run tests with verbose logging
pnpm run test:debug

# Run linting
pnpm run lint

# Fix linting issues
pnpm run lint:fix
```

### Packaging Commands
```bash
# Package extension (production)
pnpm run package-extension-pnpm

# Package for release
pnpm run package-for-release
```

### Development Tools
- **Package Manager**: pnpm (v9)
- **Build Tool**: esbuild
- **TypeScript**: For type checking and compilation
- **Testing**: Jest for unit tests, WebdriverIO for e2e tests
- **Linting**: ESLint with TypeScript plugin

## Key Configuration Areas

### Network Optimization Settings
Found in `package.json` under `augment.advanced.networkOptimization`:
- Connection and request timeouts
- Retry mechanisms
- Keep-alive settings
- Concurrency limits

### Code Completion Settings
Found in `package.json` under `augment.advanced.completions`:
- Timeout and retry configurations
- Caching and debouncing settings

### Next Edit Settings
Found in `package.json` under `augment.nextEdit`:
- Background suggestion controls
- Delay settings

## Important Dependencies

### Core AI Libraries
- `@anthropic-ai/sdk` - Anthropic API client
- `@anthropic-ai/vertex-sdk` - Anthropic Vertex AI integration

### UI Frameworks
- `monaco-editor` - Code editor component
- Custom webview components for VSCode panels

### Data Management
- `level` - Database for storing extension data
- `diff` - Text difference computation
- `fuse.js` - Fuzzy search capabilities

### Utilities
- `lodash` - General utility functions
- `uuid` - Unique identifier generation
- `semver` - Semantic version handling
- `simple-git` - Git operations

## Modifying the Extension

### Configuration Changes
All configuration changes should be made in `package.json` under the `contributes.configuration` section. Ensure default values are set properly in the `default` object rather than just in property definitions.

### Building After Changes
1. Run `pnpm run build` for development builds
2. Run `pnpm run build:prod` for production builds
3. Package with `pnpm run package-extension-pnpm`

### Testing Changes
1. Use `pnpm run test` to run all tests
2. Use `pnpm run test:debug` for verbose output
3. Manually test in VSCode by installing the generated .vsix file

## Packaging and Distribution

### Creating VSIX Package
```bash
pnpm run package-extension-pnpm
```

### Installation for Testing
```bash
code --install-extension [filename].vsix
```

### Version Management
Update version numbers in `package.json` and document changes in `CHANGELOG.md` before packaging.

## Network Optimization Details

The modified version includes several key optimizations for improved performance in challenging network environments:

1. **Extended Timeouts**: Connection (30s) and request (60s) timeouts prevent premature failures
2. **Retry Logic**: Configurable retry counts (up to 5) with progressive delays
3. **Caching**: Local caching reduces redundant network requests
4. **Request Management**: Debouncing, throttling, and queuing control request frequency
5. **Connection Reuse**: HTTP Keep-Alive improves connection efficiency
6. **Concurrency Control**: Limits concurrent requests to prevent overload
7. **Proxy Support**: Built-in proxy configuration for improved connectivity

These optimizations are automatically enabled through default configuration values, requiring no manual setup from users.