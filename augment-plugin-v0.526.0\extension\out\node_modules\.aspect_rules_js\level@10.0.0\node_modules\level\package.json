{"name": "level", "version": "10.0.0", "description": "Universal abstract-level database for Node.js and browsers", "license": "MIT", "main": "index.js", "types": "./index.d.ts", "scripts": {"test": "standard && nyc node test.js", "test-browsers-local": "airtap --coverage test.js && nyc report", "coverage": "nyc report -r lcovonly"}, "files": ["browser.js", "index.js", "index.d.ts", "CHANGELOG.md", "UPGRADING.md"], "browser": "browser.js", "dependencies": {"abstract-level": "^3.1.0", "browser-level": "^3.0.0", "classic-level": "^3.0.0"}, "devDependencies": {"@babel/preset-env": "^7.26.9", "@types/node": "^22.10.1", "@voxpelli/tsconfig": "^15.1.0", "airtap": "^5.0.0", "airtap-playwright": "^1.0.1", "babelify": "^10.0.0", "hallmark": "^5.0.1", "nyc": "^17.1.0", "standard": "^17.1.2", "tape": "^5.9.0", "typescript": "^5.7.2", "uuid": "^11.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/level"}, "repository": {"type": "git", "url": "https://github.com/Level/level.git"}, "homepage": "https://github.com/Level/level", "keywords": ["level", "leveldb", "stream", "database", "db", "store", "storage", "json"], "engines": {"node": ">=18"}}