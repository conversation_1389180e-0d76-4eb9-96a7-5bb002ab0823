#include <BaseTsd.h>
typedef SSIZE_T ssize_t;

/* Define to 1 if you have the <windows.h> header file. */
#define HAVE_WINDOWS_H 1

/* Name of package */
#define PACKAGE "snappy"

/* Define to the address where bug reports for this package should be sent. */
#define PACKAGE_BUGREPORT ""

/* Define to the full name of this package. */
#define PACKAGE_NAME "snappy"

/* Define to the full name and version of this package. */
#define PACKAGE_STRING "snappy 1.1.4"

/* Define to the one symbol short name of this package. */
#define PACKAGE_TARNAME "snappy"

/* Define to the version of this package. */
#define PACKAGE_VERSION "1.1.4"

/* Define to 1 if you have the ANSI C header files. */
#define STDC_HEADERS 1

/* Version number of package */
#define VERSION "1.1.4"
